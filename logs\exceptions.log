{"timestamp":"2025-08-07 13:24:23.635","level":"ERROR","component":"SYSTEM","message":"uncaughtException: Foodics API credentials are required\nError: Foodics API credentials are required\n    at new FoodicsApiClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js:13:13)\n    at new FoodicsService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js:13:22)\n    at new SyncService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:18:27)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:561:21)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)\n    at Module._load (node:internal/modules/cjs/loader:1023:12)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)","error":{},"stack":"Error: Foodics API credentials are required\n    at new FoodicsApiClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js:13:13)\n    at new FoodicsService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js:13:22)\n    at new SyncService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:18:27)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:561:21)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)\n    at Module._load (node:internal/modules/cjs/loader:1023:12)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)","exception":true,"date":"Thu Aug 07 2025 13:24:23 GMT+0500 (Pakistan Standard Time)","process":{"pid":5760,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api","execPath":"C:\\nvm4w\\nodejs\\node.exe","version":"v20.11.1","argv":["C:\\nvm4w\\nodejs\\node.exe","C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\app.js"],"memoryUsage":{"rss":55808000,"heapTotal":32100352,"heapUsed":19096968,"external":3934898,"arrayBuffers":17193}},"os":{"loadavg":[0,0,0],"uptime":523544.14},"trace":[{"column":13,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js","function":"new FoodicsApiClient","line":13,"method":null,"native":false},{"column":22,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js","function":"new FoodicsService","line":13,"method":null,"native":false},{"column":27,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js","function":"new SyncService","line":18,"method":null,"native":false},{"column":21,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js","function":null,"line":561,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1207,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1023,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false}],"service":"foodics-netsuite-integration","version":"1.0.0","environment":"development"}
{"timestamp":"2025-08-07 13:24:31.405","level":"ERROR","component":"SYSTEM","message":"uncaughtException: Foodics API credentials are required\nError: Foodics API credentials are required\n    at new FoodicsApiClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js:13:13)\n    at new FoodicsService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js:13:22)\n    at new SyncService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:18:27)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:561:21)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)\n    at Module._load (node:internal/modules/cjs/loader:1023:12)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)","error":{},"stack":"Error: Foodics API credentials are required\n    at new FoodicsApiClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js:13:13)\n    at new FoodicsService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js:13:22)\n    at new SyncService (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:18:27)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js:561:21)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)\n    at Module._load (node:internal/modules/cjs/loader:1023:12)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)","exception":true,"date":"Thu Aug 07 2025 13:24:31 GMT+0500 (Pakistan Standard Time)","process":{"pid":5116,"uid":null,"gid":null,"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api","execPath":"C:\\nvm4w\\nodejs\\node.exe","version":"v20.11.1","argv":["C:\\nvm4w\\nodejs\\node.exe","C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\app.js"],"memoryUsage":{"rss":56729600,"heapTotal":33935360,"heapUsed":17073784,"external":3958981,"arrayBuffers":17369}},"os":{"loadavg":[0,0,0],"uptime":523551.906},"trace":[{"column":13,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsApi.js","function":"new FoodicsApiClient","line":13,"method":null,"native":false},{"column":22,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\foodicsService.js","function":"new FoodicsService","line":13,"method":null,"native":false},{"column":27,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js","function":"new SyncService","line":18,"method":null,"native":false},{"column":21,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\backend integration api\\src\\services\\syncService.js","function":null,"line":561,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1207,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1023,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false}],"service":"foodics-netsuite-integration","version":"1.0.0","environment":"development"}
